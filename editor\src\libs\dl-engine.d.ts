/**
 * DL引擎类型声明文件
 */

declare module './dl-engine.mjs' {
  // 引擎选项接口
  export interface EngineOptions {
    canvas?: HTMLCanvasElement | string;
    autoStart?: boolean;
    debug?: boolean;
    language?: string;
  }

  // 引擎核心类
  export class Engine {
    world: World;
    constructor(options?: EngineOptions);
    initialize(): void;
    start(): void;
    stop(): void;
    dispose(): void;
    isRunning(): boolean;
  }

  // 世界类
  export class World {
    createScene(name?: string): Scene;
  }

  // 场景类
  export class Scene {
    id: string;
    name: string;
    createEntity(name: string): Entity;
    removeEntity(entity: Entity): void;
    getEntities(): Entity[];
    addEntity(entity: Entity): void;
    dispose(): void;
  }

  // 实体类
  export class Entity {
    id: string;
    name: string;
    hasComponent(type: string): boolean;
    getComponent(type: string): any;
    addComponent(type: string): any;
    getComponents(): Map<string, any>;
    isActive(): boolean;
    setParent(parent: Entity): void;
    getTransform(): any;
  }

  // 相机类
  export class Camera {
    // 相机相关属性和方法
  }

  // 向量类
  export class Vector3 {
    x: number;
    y: number;
    z: number;
    constructor(x?: number, y?: number, z?: number);
  }

  // 事件发射器类
  export class EventEmitter {
    on(event: string, listener: Function): this;
    off(event: string, listener?: Function): this;
    emit(event: string, ...args: any[]): boolean;
    removeAllListeners(): this;
  }
}
