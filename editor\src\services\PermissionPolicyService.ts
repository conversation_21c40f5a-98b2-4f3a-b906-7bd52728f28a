/**
 * 权限策略服务
 * 用于管理权限策略，实现细粒度权限控制
 */
import { EventEmitter } from '../utils/EventEmitter';
import { store } from '../store';
import { Permission } from './PermissionService';
import { CollaborationRole } from './CollaborationService';
import { permissionLogService } from './PermissionLogService';
import { collaborationService } from './CollaborationService';

/**
 * 权限策略类型
 */
export enum PolicyType {
  /** 用户策略 */
  USER = 'user',
  /** 角色策略 */
  ROLE = 'role',
  /** 组织策略 */
  ORGANIZATION = 'organization',
  /** 资源策略 */
  RESOURCE = 'resource',
  /** 时间策略 */
  TIME = 'time',
  /** 条件策略 */
  CONDITION = 'condition',
}

/**
 * 权限策略接口
 */
export interface PermissionPolicy {
  /** 策略ID */
  id: string;
  /** 策略名称 */
  name: string;
  /** 策略描述 */
  description?: string;
  /** 策略类型 */
  type: PolicyType;
  /** 策略版本 */
  version: number;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 创建者ID */
  createdBy: string;
  /** 更新者ID */
  updatedBy?: string;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
  /** 权限列表 */
  permissions: Permission[];
  /** 角色列表 */
  roles?: CollaborationRole[];
  /** 用户ID列表 */
  userIds?: string[];
  /** 组织ID列表 */
  organizationIds?: string[];
  /** 资源类型列表 */
  resourceTypes?: string[];
  /** 资源ID列表 */
  resourceIds?: string[];
  /** 时间限制 */
  timeRestrictions?: {
    /** 开始时间 */
    startTime?: number;
    /** 结束时间 */
    endTime?: number;
    /** 星期几 (0-6, 0表示星期日) */
    daysOfWeek?: number[];
    /** 小时 (0-23) */
    hours?: number[];
  };
  /** 条件表达式 */
  conditions?: {
    /** 条件类型 */
    type: string;
    /** 条件值 */
    value: any;
  }[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 权限策略服务配置
 */
export interface PermissionPolicyServiceConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用策略缓存 */
  enableCache?: boolean;
  /** 缓存过期时间（毫秒） */
  cacheExpiration?: number;
  /** 是否启用策略评估日志 */
  enableEvaluationLog?: boolean;
  /** 是否启用策略冲突检测 */
  enableConflictDetection?: boolean;
  /** 是否启用策略版本控制 */
  enableVersioning?: boolean;
  /** 最大策略数量 */
  maxPolicies?: number;
}

/**
 * 权限策略服务类
 */
class PermissionPolicyService extends EventEmitter {
  /** 配置 */
  private config: Required<PermissionPolicyServiceConfig>;
  
  /** 策略映射表 */
  private policies: Map<string, PermissionPolicy> = new Map();
  
  /** 策略评估缓存 */
  private evaluationCache: Map<string, { result: boolean; timestamp: number }> = new Map();
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: PermissionPolicyServiceConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      enabled: config.enabled ?? true,
      enableCache: config.enableCache ?? true,
      cacheExpiration: config.cacheExpiration ?? 60000, // 1分钟
      enableEvaluationLog: config.enableEvaluationLog ?? true,
      enableConflictDetection: config.enableConflictDetection ?? true,
      enableVersioning: config.enableVersioning ?? true,
      maxPolicies: config.maxPolicies ?? 1000,
      ...config,
    };
  }
  
  /**
   * 创建策略
   * @param policy 策略对象
   * @returns 创建的策略
   */
  public createPolicy(policy: Omit<PermissionPolicy, 'id' | 'createdAt' | 'updatedAt' | 'version' | 'createdBy'>): PermissionPolicy {
    // 检查是否超出最大策略数量
    if (this.policies.size >= this.config.maxPolicies) {
      throw new Error(`Maximum number of policies (${this.config.maxPolicies}) reached`);
    }
    
    // 创建策略ID
    const id = this.generateId();
    
    // 创建完整策略对象
    const now = Date.now();
    const fullPolicy: PermissionPolicy = {
      ...policy,
      id,
      version: 1,
      createdAt: now,
      updatedAt: now,
      createdBy: collaborationService.getUserId(),
    };
    
    // 添加到策略映射表
    this.policies.set(id, fullPolicy);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyCreated(
      fullPolicy.createdBy,
      id,
      fullPolicy.name,
      { type: fullPolicy.type, permissions: fullPolicy.permissions }
    );
    
    // 触发事件
    this.emit('policyCreated', fullPolicy);
    
    // 清除缓存
    this.clearCache();
    
    return fullPolicy;
  }
  
  /**
   * 更新策略
   * @param id 策略ID
   * @param updates 更新内容
   * @returns 更新后的策略
   */
  public updatePolicy(id: string, updates: Partial<Omit<PermissionPolicy, 'id' | 'createdAt' | 'version' | 'createdBy'>>): PermissionPolicy | null {
    const policy = this.policies.get(id);
    if (!policy) {
      return null;
    }
    
    // 创建更新后的策略对象
    const updatedPolicy: PermissionPolicy = {
      ...policy,
      ...updates,
      updatedAt: Date.now(),
      updatedBy: collaborationService.getUserId(),
    };
    
    // 如果启用了版本控制，增加版本号
    if (this.config.enableVersioning) {
      updatedPolicy.version = policy.version + 1;
    }
    
    // 更新策略映射表
    this.policies.set(id, updatedPolicy);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyUpdated(
      updatedPolicy.updatedBy!,
      id,
      updates
    );
    
    // 触发事件
    this.emit('policyUpdated', updatedPolicy);
    
    // 清除缓存
    this.clearCache();
    
    return updatedPolicy;
  }
  
  /**
   * 删除策略
   * @param id 策略ID
   * @returns 是否成功
   */
  public deletePolicy(id: string): boolean {
    const policy = this.policies.get(id);
    if (!policy) {
      return false;
    }
    
    // 从策略映射表中移除
    this.policies.delete(id);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyDeleted(
      collaborationService.getUserId(),
      id,
      policy.name
    );
    
    // 触发事件
    this.emit('policyDeleted', policy);
    
    // 清除缓存
    this.clearCache();
    
    return true;
  }
  
  /**
   * 获取策略
   * @param id 策略ID
   * @returns 策略对象
   */
  public getPolicy(id: string): PermissionPolicy | undefined {
    return this.policies.get(id);
  }
  
  /**
   * 获取所有策略
   * @returns 策略列表
   */
  public getAllPolicies(): PermissionPolicy[] {
    return Array.from(this.policies.values());
  }
  
  /**
   * 获取指定类型的策略
   * @param type 策略类型
   * @returns 策略列表
   */
  public getPoliciesByType(type: PolicyType): PermissionPolicy[] {
    return Array.from(this.policies.values()).filter(policy => policy.type === type);
  }
  
  /**
   * 应用策略
   * @param policyId 策略ID
   * @param targetId 目标ID
   * @param targetType 目标类型
   * @returns 是否成功
   */
  public applyPolicy(policyId: string, targetId: string, targetType: string): boolean {
    const policy = this.policies.get(policyId);
    if (!policy) {
      return false;
    }
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyApplied(
      collaborationService.getUserId(),
      policyId,
      targetId,
      targetType
    );
    
    // 触发事件
    this.emit('policyApplied', policy, targetId, targetType);
    
    // 清除缓存
    this.clearCache();
    
    return true;
  }
  
  /**
   * 评估用户是否有权限
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 是否有权限
   */
  public evaluatePermission(userId: string, permission: Permission, context?: any): boolean {
    if (!this.config.enabled) {
      return true;
    }
    
    // 创建缓存键
    const cacheKey = `${userId}:${permission}:${JSON.stringify(context || {})}`;
    
    // 检查缓存
    if (this.config.enableCache) {
      const cached = this.evaluationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.config.cacheExpiration) {
        return cached.result;
      }
    }
    
    // 获取适用的策略
    const applicablePolicies = this.getApplicablePolicies(userId, permission, context);
    
    // 如果没有适用的策略，返回false
    if (applicablePolicies.length === 0) {
      this.cacheResult(cacheKey, false);
      return false;
    }
    
    // 按优先级排序
    applicablePolicies.sort((a, b) => b.priority - a.priority);
    
    // 评估策略
    const result = this.evaluatePolicies(applicablePolicies, userId, permission, context);
    
    // 缓存结果
    this.cacheResult(cacheKey, result);
    
    return result;
  }
  
  /**
   * 获取适用的策略
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 策略列表
   * @private
   */
  private getApplicablePolicies(userId: string, permission: Permission, context?: any): PermissionPolicy[] {
    return Array.from(this.policies.values()).filter(policy => {
      // 检查策略是否启用
      if (!policy.enabled) {
        return false;
      }
      
      // 检查策略是否包含权限
      if (!policy.permissions.includes(permission)) {
        return false;
      }
      
      // 根据策略类型进行过滤
      switch (policy.type) {
        case PolicyType.USER:
          // 检查用户ID是否匹配
          return policy.userIds?.includes(userId) || false;
          
        case PolicyType.ROLE:
          // 检查用户角色是否匹配
          // 这里需要获取用户角色，然后检查策略中是否包含该角色
          // 暂时返回true，实际实现需要根据具体情况调整
          return true;
          
        case PolicyType.ORGANIZATION:
          // 检查用户所属组织是否匹配
          // 这里需要获取用户所属组织，然后检查策略中是否包含该组织
          // 暂时返回true，实际实现需要根据具体情况调整
          return true;
          
        case PolicyType.RESOURCE:
          // 检查资源类型和ID是否匹配
          if (!context || !context.resourceType || !context.resourceId) {
            return false;
          }
          return (
            (!policy.resourceTypes || policy.resourceTypes.includes(context.resourceType)) &&
            (!policy.resourceIds || policy.resourceIds.includes(context.resourceId))
          );
          
        case PolicyType.TIME:
          // 检查时间限制
          if (!policy.timeRestrictions) {
            return true;
          }
          
          const now = new Date();
          const currentTime = now.getTime();
          const dayOfWeek = now.getDay();
          const hour = now.getHours();
          
          return (
            (!policy.timeRestrictions.startTime || currentTime >= policy.timeRestrictions.startTime) &&
            (!policy.timeRestrictions.endTime || currentTime <= policy.timeRestrictions.endTime) &&
            (!policy.timeRestrictions.daysOfWeek || policy.timeRestrictions.daysOfWeek.includes(dayOfWeek)) &&
            (!policy.timeRestrictions.hours || policy.timeRestrictions.hours.includes(hour))
          );
          
        case PolicyType.CONDITION:
          // 检查条件
          // 这里需要根据条件类型和值进行评估
          // 暂时返回true，实际实现需要根据具体情况调整
          return true;
          
        default:
          return false;
      }
    });
  }
  
  /**
   * 评估策略
   * @param policies 策略列表
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 是否有权限
   * @private
   */
  private evaluatePolicies(policies: PermissionPolicy[], userId: string, permission: Permission, context?: any): boolean {
    // 如果没有策略，返回false
    if (policies.length === 0) {
      return false;
    }
    
    // 获取优先级最高的策略
    const highestPriorityPolicy = policies[0];
    
    // 记录策略评估日志
    if (this.config.enableEvaluationLog) {
      // 这里可以记录策略评估日志
    }
    
    // 返回评估结果
    return true;
  }
  
  /**
   * 缓存结果
   * @param key 缓存键
   * @param result 结果
   * @private
   */
  private cacheResult(key: string, result: boolean): void {
    if (this.config.enableCache) {
      this.evaluationCache.set(key, {
        result,
        timestamp: Date.now(),
      });
    }
  }
  
  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.evaluationCache.clear();
  }
  
  /**
   * 生成ID
   * @private
   */
  private generateId(): string {
    return `policy_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }
  
  /**
   * 销毁
   */
  public destroy(): void {
    // 清空策略
    this.policies.clear();
    
    // 清空缓存
    this.evaluationCache.clear();
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}

// 创建单例实例
export const permissionPolicyService = new PermissionPolicyService();
