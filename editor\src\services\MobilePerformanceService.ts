/**
 * 移动设备性能优化服务
 * 提供移动设备性能优化和自动调整功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import MobileDeviceService, { DeviceType } from './MobileDeviceService';

// 性能级别枚举
export enum PerformanceLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra',
  AUTO = 'auto'
}

// 性能配置接口
export interface PerformanceConfig {
  // 渲染分辨率缩放比例
  renderScale: number;
  // 最大纹理尺寸
  maxTextureSize: number;
  // 最大可见距离
  maxVisibleDistance: number;
  // 阴影质量
  shadowQuality: 'off' | 'low' | 'medium' | 'high';
  // 后处理效果
  postProcessing: boolean;
  // 抗锯齿
  antialiasing: boolean;
  // 环境光遮蔽
  ambientOcclusion: boolean;
  // 反射
  reflections: boolean;
  // 最大粒子数量
  maxParticles: number;
  // 最大骨骼数量
  maxBones: number;
  // 最大灯光数量
  maxLights: number;
  // 更新频率（帧率）
  updateFrequency: number;
  // 物理更新频率
  physicsUpdateFrequency: number;
  // LOD距离缩放
  lodDistanceScale: number;
  // 是否启用实例化渲染
  enableInstancing: boolean;
  // 是否启用GPU剔除
  enableGPUCulling: boolean;
  // 是否启用纹理压缩
  enableTextureCompression: boolean;
  // 是否启用几何体压缩
  enableGeometryCompression: boolean;
  // 是否启用动态分辨率
  enableDynamicResolution: boolean;
}

// 性能监控数据接口
export interface PerformanceMonitorData {
  // 帧率
  fps: number;
  // 渲染时间（毫秒）
  renderTime: number;
  // 物理时间（毫秒）
  physicsTime: number;
  // 更新时间（毫秒）
  updateTime: number;
  // 总时间（毫秒）
  totalTime: number;
  // GPU使用率（百分比）
  gpuUsage: number;
  // CPU使用率（百分比）
  cpuUsage: number;
  // 内存使用量（MB）
  memoryUsage: number;
  // 电池电量（百分比）
  batteryLevel: number;
  // 是否正在充电
  isCharging: boolean;
  // 温度（摄氏度）
  temperature: number;
  // 网络类型
  networkType: string;
  // 网络速度（Mbps）
  networkSpeed: number;
}

// 移动设备性能优化服务配置接口
export interface MobilePerformanceServiceConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 是否启用自动性能优化
  enableAutoOptimization?: boolean;
  // 是否启用电池优化
  enableBatteryOptimization?: boolean;
  // 是否启用温度优化
  enableTemperatureOptimization?: boolean;
  // 是否启用网络优化
  enableNetworkOptimization?: boolean;
  // 是否启用后台暂停
  enableBackgroundPause?: boolean;
  // 性能监控间隔（毫秒）
  monitorInterval?: number;
  // 性能调整间隔（毫秒）
  adjustInterval?: number;
  // 目标帧率
  targetFPS?: number;
  // 最小可接受帧率
  minAcceptableFPS?: number;
  // 默认性能级别
  defaultPerformanceLevel?: PerformanceLevel;
  // 低电量阈值（百分比）
  lowBatteryThreshold?: number;
  // 高温阈值（摄氏度）
  highTemperatureThreshold?: number;
}

// 移动设备性能优化服务事件类型
export enum MobilePerformanceEventType {
  PERFORMANCE_LEVEL_CHANGED = 'performanceLevelChanged',
  PERFORMANCE_CONFIG_CHANGED = 'performanceConfigChanged',
  PERFORMANCE_MONITOR_UPDATE = 'performanceMonitorUpdate',
  BATTERY_LOW = 'batteryLow',
  TEMPERATURE_HIGH = 'temperatureHigh',
  NETWORK_CHANGE = 'networkChange',
  BACKGROUND_STATE_CHANGE = 'backgroundStateChange'
}

/**
 * 移动设备性能优化服务类
 * 提供移动设备性能优化和自动调整功能
 */
export class MobilePerformanceService extends EventEmitter {
  private static instance: MobilePerformanceService;
  private mobileDeviceService: MobileDeviceService;
  private config: MobilePerformanceServiceConfig;

  // 性能级别
  private currentPerformanceLevel: PerformanceLevel = PerformanceLevel.AUTO;

  // 性能配置
  private performanceConfigs: Record<PerformanceLevel, PerformanceConfig> = {
    [PerformanceLevel.LOW]: {
      renderScale: 0.5,
      maxTextureSize: 1024,
      maxVisibleDistance: 100,
      shadowQuality: 'off',
      postProcessing: false,
      antialiasing: false,
      ambientOcclusion: false,
      reflections: false,
      maxParticles: 100,
      maxBones: 30,
      maxLights: 4,
      updateFrequency: 30,
      physicsUpdateFrequency: 30,
      lodDistanceScale: 0.5,
      enableInstancing: true,
      enableGPUCulling: true,
      enableTextureCompression: true,
      enableGeometryCompression: true,
      enableDynamicResolution: true
    },
    [PerformanceLevel.MEDIUM]: {
      renderScale: 0.75,
      maxTextureSize: 2048,
      maxVisibleDistance: 200,
      shadowQuality: 'low',
      postProcessing: false,
      antialiasing: true,
      ambientOcclusion: false,
      reflections: false,
      maxParticles: 500,
      maxBones: 60,
      maxLights: 8,
      updateFrequency: 45,
      physicsUpdateFrequency: 45,
      lodDistanceScale: 0.75,
      enableInstancing: true,
      enableGPUCulling: true,
      enableTextureCompression: true,
      enableGeometryCompression: true,
      enableDynamicResolution: true
    },
    [PerformanceLevel.HIGH]: {
      renderScale: 1.0,
      maxTextureSize: 4096,
      maxVisibleDistance: 500,
      shadowQuality: 'medium',
      postProcessing: true,
      antialiasing: true,
      ambientOcclusion: true,
      reflections: false,
      maxParticles: 2000,
      maxBones: 120,
      maxLights: 16,
      updateFrequency: 60,
      physicsUpdateFrequency: 60,
      lodDistanceScale: 1.0,
      enableInstancing: true,
      enableGPUCulling: true,
      enableTextureCompression: true,
      enableGeometryCompression: true,
      enableDynamicResolution: false
    },
    [PerformanceLevel.ULTRA]: {
      renderScale: 1.0,
      maxTextureSize: 8192,
      maxVisibleDistance: 1000,
      shadowQuality: 'high',
      postProcessing: true,
      antialiasing: true,
      ambientOcclusion: true,
      reflections: true,
      maxParticles: 5000,
      maxBones: 200,
      maxLights: 32,
      updateFrequency: 60,
      physicsUpdateFrequency: 60,
      lodDistanceScale: 1.5,
      enableInstancing: true,
      enableGPUCulling: true,
      enableTextureCompression: false,
      enableGeometryCompression: false,
      enableDynamicResolution: false
    },
    [PerformanceLevel.AUTO]: {
      // AUTO级别的配置将根据设备性能动态调整
      renderScale: 0.75,
      maxTextureSize: 2048,
      maxVisibleDistance: 200,
      shadowQuality: 'low',
      postProcessing: false,
      antialiasing: true,
      ambientOcclusion: false,
      reflections: false,
      maxParticles: 500,
      maxBones: 60,
      maxLights: 8,
      updateFrequency: 45,
      physicsUpdateFrequency: 45,
      lodDistanceScale: 0.75,
      enableInstancing: true,
      enableGPUCulling: true,
      enableTextureCompression: true,
      enableGeometryCompression: true,
      enableDynamicResolution: true
    }
  };

  // 当前性能配置
  private currentConfig: PerformanceConfig;

  // 性能监控数据
  private monitorData: PerformanceMonitorData = {
    fps: 60,
    renderTime: 0,
    physicsTime: 0,
    updateTime: 0,
    totalTime: 0,
    gpuUsage: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    batteryLevel: 100,
    isCharging: true,
    temperature: 0,
    networkType: 'unknown',
    networkSpeed: 0
  };

  // 监控定时器ID
  private monitorTimerId: number | null = null;

  // 调整定时器ID
  private adjustTimerId: number | null = null;

  // 帧计数器
  private frameCount: number = 0;

  // 上一次帧时间
  private lastFrameTime: number = 0;

  // 是否在后台
  private isInBackground: boolean = false;

  // 电池API
  private batteryManager: any = null;

  /**
   * 获取单例实例
   * @returns 移动设备性能优化服务实例
   */
  public static getInstance(): MobilePerformanceService {
    if (!MobilePerformanceService.instance) {
      MobilePerformanceService.instance = new MobilePerformanceService();
    }
    return MobilePerformanceService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.mobileDeviceService = MobileDeviceService.getInstance();

    // 默认配置
    this.config = {
      debug: false,
      enableAutoOptimization: true,
      enableBatteryOptimization: true,
      enableTemperatureOptimization: true,
      enableNetworkOptimization: true,
      enableBackgroundPause: true,
      monitorInterval: 1000,
      adjustInterval: 5000,
      targetFPS: 60,
      minAcceptableFPS: 30,
      defaultPerformanceLevel: PerformanceLevel.AUTO,
      lowBatteryThreshold: 20,
      highTemperatureThreshold: 40
    };

    // 设置当前性能级别和配置
    this.currentPerformanceLevel = this.config.defaultPerformanceLevel!;
    this.currentConfig = { ...this.performanceConfigs[this.currentPerformanceLevel] };

    // 初始化电池API
    this.initializeBatteryAPI();

    // 添加可见性变化事件监听
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    // 添加窗口焦点事件监听
    if (typeof window !== 'undefined') {
      window.addEventListener('focus', this.handleWindowFocus.bind(this));
      window.addEventListener('blur', this.handleWindowBlur.bind(this));
    }
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<MobilePerformanceServiceConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.config.debug) {
      console.log('移动设备性能优化服务配置已更新', this.config);
    }

    // 如果启用自动优化，则启动监控和调整
    if (this.config.enableAutoOptimization) {
      this.startMonitoring();
      this.startAutoAdjustment();
    } else {
      this.stopMonitoring();
      this.stopAutoAdjustment();
    }
  }
}

  /**
   * 初始化电池API
   */
  private initializeBatteryAPI(): void {
    if (typeof navigator !== 'undefined' && 'getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        this.batteryManager = battery;

        // 更新电池状态
        this.updateBatteryStatus();

        // 添加电池事件监听
        battery.addEventListener('levelchange', this.updateBatteryStatus.bind(this));
        battery.addEventListener('chargingchange', this.updateBatteryStatus.bind(this));
      }).catch((error: any) => {
        if (this.config.debug) {
          console.error('获取电池API失败', error);
        }
      });
    }
  }

  /**
   * 更新电池状态
   */
  private updateBatteryStatus(): void {
    if (!this.batteryManager) {
      return;
    }

    this.monitorData.batteryLevel = this.batteryManager.level * 100;
    this.monitorData.isCharging = this.batteryManager.charging;

    // 检查低电量
    if (this.config.enableBatteryOptimization &&
        this.monitorData.batteryLevel <= this.config.lowBatteryThreshold! &&
        !this.monitorData.isCharging) {
      // 发出低电量事件
      this.emit(MobilePerformanceEventType.BATTERY_LOW, {
        level: this.monitorData.batteryLevel,
        isCharging: this.monitorData.isCharging
      });

      // 如果不是已经在低性能模式，则切换到低性能模式
      if (this.currentPerformanceLevel !== PerformanceLevel.LOW) {
        this.setPerformanceLevel(PerformanceLevel.LOW);
      }
    }
  }

  /**
   * 启动性能监控
   */
  public startMonitoring(): void {
    if (this.monitorTimerId !== null) {
      return;
    }

    // 重置帧计数器
    this.frameCount = 0;
    this.lastFrameTime = performance.now();

    // 添加帧率监听
    if (typeof window !== 'undefined') {
      window.requestAnimationFrame(this.countFrame.bind(this));
    }

    // 启动监控定时器
    this.monitorTimerId = window.setInterval(() => {
      this.updateMonitorData();
    }, this.config.monitorInterval);

    if (this.config.debug) {
      console.log('性能监控已启动');
    }
  }

  /**
   * 停止性能监控
   */
  public stopMonitoring(): void {
    if (this.monitorTimerId !== null) {
      clearInterval(this.monitorTimerId);
      this.monitorTimerId = null;

      if (this.config.debug) {
        console.log('性能监控已停止');
      }
    }
  }

  /**
   * 启动自动性能调整
   */
  public startAutoAdjustment(): void {
    if (this.adjustTimerId !== null) {
      return;
    }

    // 启动调整定时器
    this.adjustTimerId = window.setInterval(() => {
      this.adjustPerformance();
    }, this.config.adjustInterval);

    if (this.config.debug) {
      console.log('自动性能调整已启动');
    }
  }

  /**
   * 停止自动性能调整
   */
  public stopAutoAdjustment(): void {
    if (this.adjustTimerId !== null) {
      clearInterval(this.adjustTimerId);
      this.adjustTimerId = null;

      if (this.config.debug) {
        console.log('自动性能调整已停止');
      }
    }
  }

  /**
   * 计数帧
   */
  private countFrame(): void {
    this.frameCount++;

    // 继续请求下一帧
    if (typeof window !== 'undefined') {
      window.requestAnimationFrame(this.countFrame.bind(this));
    }
  }

  /**
   * 更新监控数据
   */
  private updateMonitorData(): void {
    const now = performance.now();
    const elapsed = now - this.lastFrameTime;

    // 计算帧率
    this.monitorData.fps = Math.round((this.frameCount * 1000) / elapsed);

    // 重置帧计数器
    this.frameCount = 0;
    this.lastFrameTime = now;

    // 模拟其他监控数据（实际项目中应该从引擎获取真实数据）
    this.monitorData.renderTime = Math.random() * 16;
    this.monitorData.physicsTime = Math.random() * 5;
    this.monitorData.updateTime = Math.random() * 5;
    this.monitorData.totalTime = this.monitorData.renderTime + this.monitorData.physicsTime + this.monitorData.updateTime;
    this.monitorData.gpuUsage = 50 + Math.random() * 30;
    this.monitorData.cpuUsage = 40 + Math.random() * 30;
    this.monitorData.memoryUsage = 500 + Math.random() * 500;
    this.monitorData.temperature = 30 + Math.random() * 20;

    // 获取网络信息
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        this.monitorData.networkType = connection.effectiveType || 'unknown';
        this.monitorData.networkSpeed = connection.downlink || 0;
      }
    }

    // 发出监控数据更新事件
    this.emit(MobilePerformanceEventType.PERFORMANCE_MONITOR_UPDATE, { ...this.monitorData });

    // 检查温度
    if (this.config.enableTemperatureOptimization &&
        this.monitorData.temperature >= this.config.highTemperatureThreshold!) {
      // 发出高温事件
      this.emit(MobilePerformanceEventType.TEMPERATURE_HIGH, {
        temperature: this.monitorData.temperature
      });

      // 如果不是已经在低性能模式，则切换到低性能模式
      if (this.currentPerformanceLevel !== PerformanceLevel.LOW) {
        this.setPerformanceLevel(PerformanceLevel.LOW);
      }
    }
  }

  /**
   * 调整性能
   */
  private adjustPerformance(): void {
    // 如果不是自动模式，则不调整
    if (this.currentPerformanceLevel !== PerformanceLevel.AUTO) {
      return;
    }

    // 根据帧率调整性能
    if (this.monitorData.fps < this.config.minAcceptableFPS!) {
      // 帧率过低，降低性能级别
      this.decreasePerformance();
    } else if (this.monitorData.fps > this.config.targetFPS! * 1.2) {
      // 帧率远高于目标，提高性能级别
      this.increasePerformance();
    }
  }

  /**
   * 提高性能级别
   */
  private increasePerformance(): void {
    // 获取当前配置的性能级别索引
    const levels = [PerformanceLevel.LOW, PerformanceLevel.MEDIUM, PerformanceLevel.HIGH, PerformanceLevel.ULTRA];
    const currentIndex = levels.indexOf(this.getEffectivePerformanceLevel());

    // 如果已经是最高级别，则不再提高
    if (currentIndex >= levels.length - 1) {
      return;
    }

    // 提高到下一个级别
    const nextLevel = levels[currentIndex + 1];
    this.applyPerformanceConfig(nextLevel);

    if (this.config.debug) {
      console.log(`性能级别提高: ${this.getEffectivePerformanceLevel()} -> ${nextLevel}`);
    }
  }

  /**
   * 降低性能级别
   */
  private decreasePerformance(): void {
    // 获取当前配置的性能级别索引
    const levels = [PerformanceLevel.LOW, PerformanceLevel.MEDIUM, PerformanceLevel.HIGH, PerformanceLevel.ULTRA];
    const currentIndex = levels.indexOf(this.getEffectivePerformanceLevel());

    // 如果已经是最低级别，则不再降低
    if (currentIndex <= 0) {
      return;
    }

    // 降低到上一个级别
    const prevLevel = levels[currentIndex - 1];
    this.applyPerformanceConfig(prevLevel);

    if (this.config.debug) {
      console.log(`性能级别降低: ${this.getEffectivePerformanceLevel()} -> ${prevLevel}`);
    }
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  public setPerformanceLevel(level: PerformanceLevel): void {
    if (level === this.currentPerformanceLevel) {
      return;
    }

    const oldLevel = this.currentPerformanceLevel;
    this.currentPerformanceLevel = level;

    // 应用性能配置
    if (level !== PerformanceLevel.AUTO) {
      this.applyPerformanceConfig(level);
    }

    // 发出性能级别变更事件
    this.emit(MobilePerformanceEventType.PERFORMANCE_LEVEL_CHANGED, {
      oldLevel,
      newLevel: level,
      config: this.currentConfig
    });

    if (this.config.debug) {
      console.log(`性能级别已设置: ${oldLevel} -> ${level}`);
    }
  }

  /**
   * 应用性能配置
   * @param level 性能级别
   */
  private applyPerformanceConfig(level: PerformanceLevel): void {
    if (level === PerformanceLevel.AUTO) {
      return;
    }

    const oldConfig = { ...this.currentConfig };
    this.currentConfig = { ...this.performanceConfigs[level] };

    // 发出性能配置变更事件
    this.emit(MobilePerformanceEventType.PERFORMANCE_CONFIG_CHANGED, {
      oldConfig,
      newConfig: this.currentConfig,
      level
    });

    if (this.config.debug) {
      console.log(`应用性能配置: ${level}`, this.currentConfig);
    }
  }

  /**
   * 获取当前性能级别
   * @returns 当前性能级别
   */
  public getPerformanceLevel(): PerformanceLevel {
    return this.currentPerformanceLevel;
  }

  /**
   * 获取有效性能级别
   * @returns 有效性能级别
   */
  public getEffectivePerformanceLevel(): PerformanceLevel {
    // 如果是自动模式，则根据设备类型确定默认级别
    if (this.currentPerformanceLevel === PerformanceLevel.AUTO) {
      const deviceInfo = this.mobileDeviceService.getDeviceInfo();

      switch (deviceInfo.type) {
        case DeviceType.MOBILE:
          return PerformanceLevel.LOW;
        case DeviceType.TABLET:
          return PerformanceLevel.MEDIUM;
        default:
          return PerformanceLevel.HIGH;
      }
    }

    return this.currentPerformanceLevel;
  }

  /**
   * 获取当前性能配置
   * @returns 当前性能配置
   */
  public getCurrentConfig(): PerformanceConfig {
    return { ...this.currentConfig };
  }

  /**
   * 获取性能监控数据
   * @returns 性能监控数据
   */
  public getMonitorData(): PerformanceMonitorData {
    return { ...this.monitorData };
  }

  /**
   * 处理可见性变化事件
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      this.handleBackgroundState();
    } else {
      this.handleForegroundState();
    }
  }

  /**
   * 处理窗口失去焦点事件
   */
  private handleWindowBlur(): void {
    this.handleBackgroundState();
  }

  /**
   * 处理窗口获得焦点事件
   */
  private handleWindowFocus(): void {
    this.handleForegroundState();
  }

  /**
   * 处理后台状态
   */
  private handleBackgroundState(): void {
    if (this.isInBackground) {
      return;
    }

    this.isInBackground = true;

    // 发出后台状态变化事件
    this.emit(MobilePerformanceEventType.BACKGROUND_STATE_CHANGE, {
      isInBackground: true
    });

    // 如果启用后台暂停，则暂停更新
    if (this.config.enableBackgroundPause) {
      // 在实际项目中，这里应该调用引擎的暂停方法
      if (this.config.debug) {
        console.log('应用进入后台，暂停更新');
      }
    }
  }

  /**
   * 处理前台状态
   */
  private handleForegroundState(): void {
    if (!this.isInBackground) {
      return;
    }

    this.isInBackground = false;

    // 发出前台状态变化事件
    this.emit(MobilePerformanceEventType.BACKGROUND_STATE_CHANGE, {
      isInBackground: false
    });

    // 如果启用后台暂停，则恢复更新
    if (this.config.enableBackgroundPause) {
      // 在实际项目中，这里应该调用引擎的恢复方法
      if (this.config.debug) {
        console.log('应用返回前台，恢复更新');
      }
    }

    // 重置帧计数器
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
  }

  /**
   * 自定义性能配置
   * @param level 性能级别
   * @param config 性能配置
   */
  public customizePerformanceConfig(level: PerformanceLevel, config: Partial<PerformanceConfig>): void {
    this.performanceConfigs[level] = { ...this.performanceConfigs[level], ...config };

    // 如果当前级别是被自定义的级别，则应用新配置
    if (this.currentPerformanceLevel === level) {
      this.applyPerformanceConfig(level);
    }

    if (this.config.debug) {
      console.log(`自定义性能配置: ${level}`, this.performanceConfigs[level]);
    }
  }
}

export default MobilePerformanceService.getInstance();
