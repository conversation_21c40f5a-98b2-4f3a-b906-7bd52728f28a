/**
 * 递归合并服务
 * 提供高级递归合并算法，用于解决复杂数据结构的冲突
 */
import { EventEmitter } from '../utils/EventEmitter';
import { cloneDeep, isArray, isObject, isEqual } from 'lodash';
import { Operation } from './CollaborationService';

/**
 * 合并策略枚举
 */
export enum MergeStrategy {
  PREFER_LOCAL = 'prefer_local',   // 冲突时优先使用本地值
  PREFER_REMOTE = 'prefer_remote', // 冲突时优先使用远程值
  DEEP_MERGE = 'deep_merge',       // 尝试深度合并
  CUSTOM = 'custom'                // 使用自定义合并函数
}

/**
 * 合并结果接口
 */
export interface MergeResult {
  merged: any;                     // 合并后的数据
  conflicts: MergeConflict[];      // 合并过程中的冲突
  success: boolean;                // 合并是否成功
}

/**
 * 合并冲突接口
 */
export interface MergeConflict {
  path: string[];                  // 冲突路径
  localValue: any;                 // 本地值
  remoteValue: any;                // 远程值
  resolvedValue: any;              // 解决后的值
  strategy: MergeStrategy;         // 使用的解决策略
}

/**
 * 自定义合并函数类型
 */
export type CustomMergeFn = (localValue: any, remoteValue: any, path: string[]) => any;

/**
 * 递归合并服务类
 */
class RecursiveMergeService extends EventEmitter {
  /**
   * 递归合并两个对象
   * @param local 本地对象
   * @param remote 远程对象
   * @param options 合并选项
   * @returns 合并结果
   */
  public mergeObjects(
    local: any,
    remote: any,
    options: {
      strategy?: MergeStrategy;
      customMergeFn?: CustomMergeFn;
      maxDepth?: number;
    } = {}
  ): MergeResult {
    const {
      strategy = MergeStrategy.DEEP_MERGE,
      customMergeFn,
      maxDepth = 10
    } = options;

    const conflicts: MergeConflict[] = [];
    
    // 执行递归合并
    const merged = this._recursiveMerge(
      local,
      remote,
      [],
      conflicts,
      strategy,
      customMergeFn,
      0,
      maxDepth
    );

    return {
      merged,
      conflicts,
      success: true
    };
  }

  /**
   * 合并操作数据
   * @param localOperation 本地操作
   * @param remoteOperation 远程操作
   * @param options 合并选项
   * @returns 合并后的操作数据
   */
  public mergeOperationData(
    localOperation: Operation,
    remoteOperation: Operation,
    options: {
      strategy?: MergeStrategy;
      customMergeFn?: CustomMergeFn;
      maxDepth?: number;
    } = {}
  ): { data: any; conflicts: MergeConflict[] } {
    const localData = localOperation.data || {};
    const remoteData = remoteOperation.data || {};

    const result = this.mergeObjects(localData, remoteData, options);

    return {
      data: result.merged,
      conflicts: result.conflicts
    };
  }

  /**
   * 递归合并实现
   * @private
   */
  private _recursiveMerge(
    local: any,
    remote: any,
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): any {
    // 防止无限递归
    if (depth > maxDepth) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? remote : local;
    }

    // 如果值相等，直接返回
    if (isEqual(local, remote)) {
      return cloneDeep(local);
    }

    // 如果一方为null或undefined
    if (local === null || local === undefined) {
      return cloneDeep(remote);
    }
    if (remote === null || remote === undefined) {
      return cloneDeep(local);
    }

    // 如果类型不同，记录冲突并根据策略返回
    if (typeof local !== typeof remote || Array.isArray(local) !== Array.isArray(remote)) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
    }

    // 处理数组
    if (isArray(local)) {
      return this._mergeArrays(local, remote, path, conflicts, strategy, customMergeFn, depth, maxDepth);
    }

    // 处理对象
    if (isObject(local)) {
      return this._mergeObjects(local, remote, path, conflicts, strategy, customMergeFn, depth, maxDepth);
    }

    // 处理基本类型
    if (local !== remote) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      
      // 使用自定义合并函数
      if (strategy === MergeStrategy.CUSTOM && customMergeFn) {
        const result = customMergeFn(local, remote, path);
        conflict.resolvedValue = result;
        return result;
      }
      
      return strategy === MergeStrategy.PREFER_REMOTE ? remote : local;
    }

    return local;
  }

  /**
   * 合并数组
   * @private
   */
  private _mergeArrays(
    local: any[],
    remote: any[],
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): any[] {
    // 如果策略不是深度合并，则根据策略选择数组
    if (strategy !== MergeStrategy.DEEP_MERGE) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
    }

    // 尝试智能合并数组
    // 这里使用一个简单的启发式方法：如果数组长度相同，则逐项合并
    if (local.length === remote.length) {
      return local.map((item, index) => {
        const itemPath = [...path, index.toString()];
        return this._recursiveMerge(
          item,
          remote[index],
          itemPath,
          conflicts,
          strategy,
          customMergeFn,
          depth + 1,
          maxDepth
        );
      });
    }

    // 如果数组长度不同，记录冲突并根据策略返回
    const conflict = this._createConflict(local, remote, path, strategy);
    conflicts.push(conflict);
    
    // 使用自定义合并函数
    if (strategy === MergeStrategy.CUSTOM && customMergeFn) {
      const result = customMergeFn(local, remote, path);
      conflict.resolvedValue = result;
      return result;
    }
    
    return strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
  }

  /**
   * 合并对象
   * @private
   */
  private _mergeObjects(
    local: Record<string, any>,
    remote: Record<string, any>,
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): Record<string, any> {
    const result: Record<string, any> = {};
    const allKeys = new Set([...Object.keys(local), ...Object.keys(remote)]);

    for (const key of allKeys) {
      const keyPath = [...path, key];
      
      // 键只存在于本地
      if (!(key in remote)) {
        result[key] = cloneDeep(local[key]);
        continue;
      }
      
      // 键只存在于远程
      if (!(key in local)) {
        result[key] = cloneDeep(remote[key]);
        continue;
      }
      
      // 键在两边都存在，递归合并
      result[key] = this._recursiveMerge(
        local[key],
        remote[key],
        keyPath,
        conflicts,
        strategy,
        customMergeFn,
        depth + 1,
        maxDepth
      );
    }

    return result;
  }

  /**
   * 创建合并冲突对象
   * @private
   */
  private _createConflict(
    localValue: any,
    remoteValue: any,
    path: string[],
    strategy: MergeStrategy
  ): MergeConflict {
    const resolvedValue = strategy === MergeStrategy.PREFER_REMOTE ? remoteValue : localValue;
    
    return {
      path,
      localValue,
      remoteValue,
      resolvedValue,
      strategy
    };
  }
}

// 导出单例
export const recursiveMergeService = new RecursiveMergeService();
