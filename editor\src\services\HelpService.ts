/**
 * 帮助服务
 * 负责管理编辑器的帮助系统，包括文档访问、上下文帮助和教程
 */
import { i18n } from '../i18n';
import { EventEmitter } from '../utils/EventEmitter';

export interface HelpTopic {
  id: string;
  title: string;
  path: string;
  keywords: string[];
  category: string;
  contextIds?: string[];
}

export interface HelpCategory {
  id: string;
  title: string;
  topics: HelpTopic[];
}

export class HelpService {
  private static instance: HelpService;
  private topics: HelpTopic[] = [];
  private categories: HelpCategory[] = [];
  private events = new EventEmitter();
  private currentLanguage: string = 'zh-CN';
  private helpBaseUrl: string = '/docs/';

  private constructor() {
    this.initializeTopics();
  }

  /**
   * 获取帮助服务实例
   */
  public static getInstance(): HelpService {
    if (!HelpService.instance) {
      HelpService.instance = new HelpService();
    }
    return HelpService.instance;
  }

  /**
   * 初始化帮助主题
   */
  private initializeTopics(): void {
    // 参考文档类别
    this.addCategory({
      id: 'reference',
      title: i18n.t('help.categories.reference'),
      topics: []
    });

    // 组件参考主题
    this.addTopic({
      id: 'components-reference',
      title: i18n.t('help.topics.componentsReference'),
      path: 'user-guide/reference/components.md',
      keywords: ['component', 'reference', 'transform', 'renderer', 'physics', 'animation'],
      category: 'reference',
      contextIds: ['component-panel', 'inspector-panel']
    });

    // 着色器参考主题
    this.addTopic({
      id: 'shaders-reference',
      title: i18n.t('help.topics.shadersReference'),
      path: 'user-guide/reference/shaders.md',
      keywords: ['shader', 'material', 'rendering', 'pbr', 'texture'],
      category: 'reference',
      contextIds: ['material-editor', 'shader-editor']
    });

    // 脚本参考主题
    this.addTopic({
      id: 'scripting-reference',
      title: i18n.t('help.topics.scriptingReference'),
      path: 'user-guide/reference/scripting.md',
      keywords: ['script', 'programming', 'api', 'code', 'javascript', 'typescript'],
      category: 'reference',
      contextIds: ['script-editor', 'code-editor']
    });

    // 命令行工具参考主题
    this.addTopic({
      id: 'cli-reference',
      title: i18n.t('help.topics.cliReference'),
      path: 'user-guide/reference/cli.md',
      keywords: ['cli', 'command', 'terminal', 'build', 'publish', 'automation'],
      category: 'reference',
      contextIds: ['terminal-panel', 'build-panel']
    });

    // 功能指南类别
    this.addCategory({
      id: 'features',
      title: i18n.t('help.categories.features'),
      topics: []
    });

    // 视觉脚本主题
    this.addTopic({
      id: 'visual-scripting',
      title: i18n.t('help.topics.visualScripting'),
      path: 'user-guide/features/visual-scripting.md',
      keywords: ['visual', 'script', 'node', 'graph', 'programming'],
      category: 'features',
      contextIds: ['visual-script-editor']
    });

    // 其他类别和主题...
  }

  /**
   * 添加帮助类别
   */
  public addCategory(category: HelpCategory): void {
    this.categories.push(category);
    this.events.emit('categoriesChanged', this.categories);
  }

  /**
   * 添加帮助主题
   */
  public addTopic(topic: HelpTopic): void {
    this.topics.push(topic);
    
    // 将主题添加到对应类别
    const category = this.categories.find(c => c.id === topic.category);
    if (category) {
      category.topics.push(topic);
    }
    
    this.events.emit('topicsChanged', this.topics);
  }

  /**
   * 获取所有帮助类别
   */
  public getCategories(): HelpCategory[] {
    return this.categories;
  }

  /**
   * 获取所有帮助主题
   */
  public getTopics(): HelpTopic[] {
    return this.topics;
  }

  /**
   * 根据ID获取帮助主题
   */
  public getTopicById(id: string): HelpTopic | undefined {
    return this.topics.find(topic => topic.id === id);
  }

  /**
   * 根据上下文ID获取相关帮助主题
   */
  public getTopicsByContextId(contextId: string): HelpTopic[] {
    return this.topics.filter(topic => topic.contextIds?.includes(contextId));
  }

  /**
   * 搜索帮助主题
   */
  public searchTopics(query: string): HelpTopic[] {
    const lowerQuery = query.toLowerCase();
    return this.topics.filter(topic => 
      topic.title.toLowerCase().includes(lowerQuery) || 
      topic.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取帮助主题的URL
   */
  public getTopicUrl(topicId: string): string {
    const topic = this.getTopicById(topicId);
    if (!topic) return '';
    
    let path = topic.path;
    
    // 根据当前语言选择文档路径
    if (this.currentLanguage !== 'zh-CN') {
      // 检查是否有对应语言的文档
      const langCode = this.currentLanguage.split('-')[0].toLowerCase();
      const pathParts = path.split('.');
      if (pathParts.length > 1) {
        path = `${pathParts[0]}.${langCode}.${pathParts[1]}`;
      } else {
        path = `${path.substring(0, path.lastIndexOf('.'))}.${langCode}${path.substring(path.lastIndexOf('.'))}`;
      }
    }
    
    return `${this.helpBaseUrl}${path}`;
  }

  /**
   * 打开帮助主题
   */
  public openTopic(topicId: string): void {
    const url = this.getTopicUrl(topicId);
    if (url) {
      this.events.emit('openTopic', url);
      // 在这里实现打开帮助文档的逻辑，例如打开内置浏览器或新窗口
    }
  }

  /**
   * 打开上下文帮助
   */
  public openContextHelp(contextId: string): void {
    const topics = this.getTopicsByContextId(contextId);
    if (topics.length > 0) {
      this.openTopic(topics[0].id);
    }
  }

  /**
   * 设置当前语言
   */
  public setLanguage(language: string): void {
    this.currentLanguage = language;
  }

  /**
   * 获取当前语言
   */
  public getLanguage(): string {
    return this.currentLanguage;
  }

  /**
   * 设置帮助基础URL
   */
  public setHelpBaseUrl(url: string): void {
    this.helpBaseUrl = url;
  }

  /**
   * 获取帮助基础URL
   */
  public getHelpBaseUrl(): string {
    return this.helpBaseUrl;
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }
}

export default HelpService.getInstance();
