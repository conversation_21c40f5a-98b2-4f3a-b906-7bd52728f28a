/**
 * 协作编辑服务
 * 负责管理WebSocket连接、消息处理和用户状态
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  setCollaborationStatus,
  addUser,
  removeUser,
  updateUserStatus,
  setActiveUsers,
  addOperation,
  setOperations
} from '../store/collaboration/collaborationSlice';
import { permissionService } from './PermissionService';
import { WebSocketConnectionManager, ConnectionStatus, ConnectionConfig } from './WebSocketConnectionManager';

// 协作状态枚举
export enum CollaborationStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// 用户角色枚举
export enum CollaborationRole {
  VIEWER = 'viewer',
  EDITOR = 'editor',
  ADMIN = 'admin',
  OWNER = 'owner'
}

// 用户状态
export interface CollaborationUser {
  id: string;
  name: string;
  avatar?: string;
  role: CollaborationRole;
  color: string;
  isActive: boolean;
  lastActivity: number;
  cursor?: {
    x: number;
    y: number;
    entityId?: string;
  };
}

// 操作类型枚举
export enum OperationType {
  ENTITY_CREATE = 'entity_create',
  ENTITY_UPDATE = 'entity_update',
  ENTITY_DELETE = 'entity_delete',
  COMPONENT_ADD = 'component_add',
  COMPONENT_UPDATE = 'component_update',
  COMPONENT_REMOVE = 'component_remove',
  SCENE_UPDATE = 'scene_update',
  CURSOR_MOVE = 'cursor_move',
  SELECTION_CHANGE = 'selection_change',
  PROPERTY_UPDATE = 'property_update',
  EDITING_ZONE_UPDATE = 'editing_zone_update',
  EDITING_ZONE_CLEAR = 'editing_zone_clear',
  LOCK_CREATE = 'lock_create',
  LOCK_RELEASE = 'lock_release',
  LOCK_RENEW = 'lock_renew',
  LOCK_FORCIBLY_RELEASE = 'lock_forcibly_release'
}

// 操作接口
export interface Operation {
  id: string;
  type: OperationType;
  userId: string;
  timestamp: number;
  data: any;
}

// 消息类型枚举
export enum MessageType {
  JOIN = 'join',
  LEAVE = 'leave',
  USER_LIST = 'user_list',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  USER_STATUS = 'user_status',
  OPERATION = 'operation',
  OPERATION_HISTORY = 'operation_history',
  ERROR = 'error'
}

// 消息接口
export interface Message {
  type: MessageType;
  data: any;
}

/**
 * 协作编辑服务类
 */
class CollaborationService extends EventEmitter {
  // WebSocket连接管理器
  public connectionManager: WebSocketConnectionManager | null = null;

  private serverUrl: string = '';
  private projectId: string = '';
  private sceneId: string = '';
  private userId: string = '';
  private userName: string = '';
  private status: CollaborationStatus = CollaborationStatus.DISCONNECTED;
  private operationQueue: Operation[] = [];
  private operationHistory: Operation[] = [];
  private users: Map<string, CollaborationUser> = new Map();

  // 连接配置
  private connectionConfig: Partial<ConnectionConfig> = {
    initialReconnectDelay: 1000,
    maxReconnectDelay: 30000,
    reconnectBackoffFactor: 1.5,
    reconnectJitter: 0.2,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
    heartbeatTimeout: 10000,
    enableCompression: true,
    compressionThreshold: 1024,
    enableMessageQueue: true,
    maxQueueSize: 100,
    enableStatusMonitoring: true,
    statusMonitoringInterval: 5000,
    autoReconnect: true,
    debug: false
  };

  /**
   * 初始化协作服务
   * @param serverUrl WebSocket服务器URL
   * @param projectId 项目ID
   * @param sceneId 场景ID
   * @param userId 用户ID
   * @param userName 用户名
   */
  public initialize(serverUrl: string, projectId: string, sceneId: string, userId: string, userName: string): void {
    this.serverUrl = serverUrl;
    this.projectId = projectId;
    this.sceneId = sceneId;
    this.userId = userId;
    this.userName = userName;

    // 创建WebSocket连接管理器
    this.connectionManager = new WebSocketConnectionManager(this.connectionConfig);

    // 设置连接参数
    const token = localStorage.getItem('token') || '';
    const params = {
      token,
      projectId: this.projectId,
      sceneId: this.sceneId,
      userId: this.userId,
      userName: this.userName
    };

    // 初始化连接管理器
    this.connectionManager.initialize(this.serverUrl + '/collaboration', params);

    // 设置事件监听器
    this.setupConnectionListeners();
  }

  /**
   * 连接到协作服务器
   */
  public connect(): void {
    if (!this.connectionManager) {
      console.error('连接管理器未初始化');
      return;
    }

    // 更新协作状态
    this.mapConnectionStatus(this.connectionManager.getStatus());

    // 连接到服务器
    this.connectionManager.connect();
  }

  /**
   * 断开与协作服务器的连接
   */
  public disconnect(): void {
    if (!this.connectionManager) {
      return;
    }

    // 断开连接
    this.connectionManager.disconnect();

    // 更新状态
    this.status = CollaborationStatus.DISCONNECTED;
    store.dispatch(setCollaborationStatus(CollaborationStatus.DISCONNECTED));

    message.info('已断开与协作服务器的连接');
  }

  /**
   * 设置连接监听器
   */
  private setupConnectionListeners(): void {
    if (!this.connectionManager) {
      return;
    }

    // 连接成功事件
    this.connectionManager.on('connected', () => {
      this.handleConnected();
    });

    // 断开连接事件
    this.connectionManager.on('disconnected', (event: any) => {
      this.handleDisconnected(event);
    });

    // 错误事件
    this.connectionManager.on('error', (event: any) => {
      this.handleError(event);
    });

    // 消息事件
    this.connectionManager.on('message', (message: any) => {
      this.handleMessage(message);
    });

    // 状态变化事件
    this.connectionManager.on('statusChange', (status: ConnectionStatus) => {
      this.mapConnectionStatus(status);
    });
  }

  /**
   * 映射连接状态到协作状态
   * @param connectionStatus 连接状态
   */
  private mapConnectionStatus(connectionStatus: ConnectionStatus): void {
    let collaborationStatus: CollaborationStatus;

    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED:
        collaborationStatus = CollaborationStatus.CONNECTED;
        break;
      case ConnectionStatus.CONNECTING:
        collaborationStatus = CollaborationStatus.CONNECTING;
        break;
      case ConnectionStatus.DISCONNECTED:
        collaborationStatus = CollaborationStatus.DISCONNECTED;
        break;
      case ConnectionStatus.RECONNECTING:
        collaborationStatus = CollaborationStatus.RECONNECTING;
        break;
      case ConnectionStatus.ERROR:
        collaborationStatus = CollaborationStatus.ERROR;
        break;
      default:
        collaborationStatus = CollaborationStatus.DISCONNECTED;
    }

    this.status = collaborationStatus;
    store.dispatch(setCollaborationStatus(collaborationStatus));
  }

  /**
   * 发送操作到服务器
   * @param operation 操作对象
   */
  public sendOperation(operation: Omit<Operation, 'id' | 'userId' | 'timestamp'>): void {
    if (this.status !== CollaborationStatus.CONNECTED) {
      // 如果未连接，将操作添加到队列
      this.operationQueue.push({
        ...operation,
        id: this.generateId(),
        userId: this.userId,
        timestamp: Date.now()
      });
      return;
    }

    const fullOperation: Operation = {
      ...operation,
      id: this.generateId(),
      userId: this.userId,
      timestamp: Date.now()
    };

    this.sendMessage(MessageType.OPERATION, fullOperation);

    // 添加到本地历史
    this.operationHistory.push(fullOperation);
    store.dispatch(addOperation(fullOperation));
  }

  // 用户状态更新节流计时器
  private userStatusThrottleTimer: number | null = null;
  // 待发送的用户状态更新
  private pendingUserStatus: Record<string, any> = {};
  // 上次发送的用户状态
  private lastSentUserStatus: Record<string, any> = {};
  // 用户状态更新节流间隔（毫秒）
  private userStatusThrottleInterval: number = 100;
  // 用户状态更新优先级
  private userStatusPriorities: Record<string, number> = {
    isActive: 1,      // 低优先级
    cursor: 2,        // 中优先级
    role: 3,          // 高优先级
    editingZone: 3    // 高优先级
  };

  /**
   * 发送用户状态更新
   * @param status 状态数据
   * @param options 选项
   */
  public sendUserStatus(
    status: any,
    options: {
      immediate?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ): void {
    if (this.status !== CollaborationStatus.CONNECTED) {
      return;
    }

    const { immediate = false, priority = 'normal' } = options;

    // 合并待发送的状态
    this.pendingUserStatus = {
      ...this.pendingUserStatus,
      ...status
    };

    // 如果要求立即发送
    if (immediate) {
      this.flushUserStatus();
      return;
    }

    // 根据优先级调整节流间隔
    let throttleInterval = this.userStatusThrottleInterval;
    if (priority === 'high') {
      throttleInterval = 50; // 高优先级，更快发送
    } else if (priority === 'low') {
      throttleInterval = 200; // 低优先级，延迟发送
    }

    // 设置节流定时器
    if (this.userStatusThrottleTimer === null) {
      this.userStatusThrottleTimer = window.setTimeout(() => {
        this.flushUserStatus();
      }, throttleInterval);
    }
  }

  /**
   * 立即发送所有待发送的用户状态
   */
  public flushUserStatus(): void {
    if (this.userStatusThrottleTimer !== null) {
      clearTimeout(this.userStatusThrottleTimer);
      this.userStatusThrottleTimer = null;
    }

    // 如果没有待发送的状态或未连接，直接返回
    if (Object.keys(this.pendingUserStatus).length === 0 || this.status !== CollaborationStatus.CONNECTED) {
      return;
    }

    // 检查是否有变化
    const hasChanges = this.hasUserStatusChanges(this.pendingUserStatus, this.lastSentUserStatus);

    if (hasChanges) {
      // 发送状态更新
      this.sendMessage(MessageType.USER_STATUS, {
        userId: this.userId,
        ...this.pendingUserStatus
      });

      // 更新最后发送的状态
      this.lastSentUserStatus = {
        ...this.lastSentUserStatus,
        ...this.pendingUserStatus
      };
    }

    // 清空待发送状态
    this.pendingUserStatus = {};
  }

  /**
   * 检查用户状态是否有变化
   * @param newStatus 新状态
   * @param oldStatus 旧状态
   * @returns 是否有变化
   */
  private hasUserStatusChanges(newStatus: Record<string, any>, oldStatus: Record<string, any>): boolean {
    // 检查每个属性是否有变化
    for (const key in newStatus) {
      // 如果旧状态没有此属性或属性值不同
      if (!(key in oldStatus) || !this.isValueEqual(newStatus[key], oldStatus[key])) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查值是否相等
   * @param value1 值1
   * @param value2 值2
   * @returns 是否相等
   */
  private isValueEqual(value1: any, value2: any): boolean {
    // 如果两个值都是对象
    if (typeof value1 === 'object' && value1 !== null && typeof value2 === 'object' && value2 !== null) {
      // 如果是数组，检查每个元素
      if (Array.isArray(value1) && Array.isArray(value2)) {
        if (value1.length !== value2.length) {
          return false;
        }

        for (let i = 0; i < value1.length; i++) {
          if (!this.isValueEqual(value1[i], value2[i])) {
            return false;
          }
        }

        return true;
      }

      // 如果是普通对象，检查每个属性
      const keys1 = Object.keys(value1);
      const keys2 = Object.keys(value2);

      if (keys1.length !== keys2.length) {
        return false;
      }

      for (const key of keys1) {
        if (!this.isValueEqual(value1[key], value2[key])) {
          return false;
        }
      }

      return true;
    }

    // 否则直接比较值
    return value1 === value2;
  }

  // 光标位置节流计时器
  private cursorThrottleTimer: number | null = null;
  // 光标位置节流间隔（毫秒）
  private cursorThrottleInterval: number = 50;
  // 上次发送的光标位置
  private lastSentCursorPosition: { x: number; y: number; entityId?: string } | null = null;

  /**
   * 发送光标位置
   * @param x X坐标
   * @param y Y坐标
   * @param entityId 实体ID（可选）
   * @param options 选项
   */
  public sendCursorPosition(
    x: number,
    y: number,
    entityId?: string,
    options: {
      immediate?: boolean;
      throttle?: boolean;
    } = {}
  ): void {
    const { immediate = false, throttle = true } = options;

    // 创建光标数据
    const cursorData = { x, y, entityId };

    // 检查是否与上次发送的位置相同
    if (this.lastSentCursorPosition &&
        this.lastSentCursorPosition.x === x &&
        this.lastSentCursorPosition.y === y &&
        this.lastSentCursorPosition.entityId === entityId) {
      return;
    }

    // 更新上次发送的位置
    this.lastSentCursorPosition = { ...cursorData };

    // 如果要求立即发送或不使用节流
    if (immediate || !throttle) {
      // 使用用户状态更新机制发送
      this.sendUserStatus({ cursor: cursorData }, { immediate: true, priority: 'high' });
      return;
    }

    // 使用节流机制
    if (this.cursorThrottleTimer !== null) {
      clearTimeout(this.cursorThrottleTimer);
    }

    this.cursorThrottleTimer = window.setTimeout(() => {
      this.cursorThrottleTimer = null;
      // 使用用户状态更新机制发送
      this.sendUserStatus({ cursor: cursorData }, { priority: 'normal' });
    }, this.cursorThrottleInterval);
  }

  // 选择变更节流计时器
  private selectionThrottleTimer: number | null = null;
  // 选择变更节流间隔（毫秒）
  private selectionThrottleInterval: number = 100;
  // 上次发送的选择
  private lastSentSelection: string[] | null = null;

  /**
   * 发送选择变更
   * @param selectedIds 选中的实体ID数组
   * @param options 选项
   */
  public sendSelectionChange(
    selectedIds: string[],
    options: {
      immediate?: boolean;
      throttle?: boolean;
    } = {}
  ): void {
    const { immediate = false, throttle = true } = options;

    // 检查是否与上次发送的选择相同
    if (this.lastSentSelection &&
        this.arraysEqual(this.lastSentSelection, selectedIds)) {
      return;
    }

    // 更新上次发送的选择
    this.lastSentSelection = [...selectedIds];

    // 如果要求立即发送或不使用节流
    if (immediate || !throttle) {
      this.sendOperation({
        type: OperationType.SELECTION_CHANGE,
        data: {
          selectedIds
        }
      });
      return;
    }

    // 使用节流机制
    if (this.selectionThrottleTimer !== null) {
      clearTimeout(this.selectionThrottleTimer);
    }

    this.selectionThrottleTimer = window.setTimeout(() => {
      this.selectionThrottleTimer = null;
      this.sendOperation({
        type: OperationType.SELECTION_CHANGE,
        data: {
          selectedIds
        }
      });
    }, this.selectionThrottleInterval);
  }

  /**
   * 比较两个数组是否相等
   * @param arr1 数组1
   * @param arr2 数组2
   * @returns 是否相等
   */
  private arraysEqual(arr1: any[], arr2: any[]): boolean {
    if (arr1.length !== arr2.length) {
      return false;
    }

    // 对于选择数组，我们只关心包含的ID，不关心顺序
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);

    if (set1.size !== set2.size) {
      return false;
    }

    for (const item of set1) {
      if (!set2.has(item)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取所有在线用户
   * @returns 用户数组
   */
  public getUsers(): CollaborationUser[] {
    return Array.from(this.users.values());
  }

  /**
   * 获取特定用户
   * @param userId 用户ID
   * @returns 用户对象或undefined
   */
  public getUser(userId: string): CollaborationUser | undefined {
    return this.users.get(userId);
  }

  /**
   * 获取操作历史
   * @returns 操作历史数组
   */
  public getOperationHistory(): Operation[] {
    return [...this.operationHistory];
  }

  /**
   * 获取当前状态
   * @returns 协作状态
   */
  public getStatus(): CollaborationStatus {
    return this.status;
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID
   */
  public getUserId(): string {
    return this.userId;
  }

  /**
   * 处理连接成功事件
   */
  private handleConnected(): void {
    console.log('已连接到协作服务器');

    // 发送加入消息
    this.sendMessage(MessageType.JOIN, {
      userId: this.userId,
      userName: this.userName,
      projectId: this.projectId,
      sceneId: this.sceneId
    });

    // 处理操作队列
    this.processOperationQueue();

    this.emit('connected');
    message.success('已连接到协作服务器');
  }

  /**
   * 处理断开连接事件
   * @param event 断开连接事件
   */
  private handleDisconnected(event: any): void {
    console.log('与协作服务器的连接已断开');
    this.emit('disconnected', event);
  }

  /**
   * 处理消息事件
   * @param message 消息对象
   */
  private handleMessage(message: Message): void {
    try {
      switch (message.type) {
        case MessageType.USER_LIST:
          this.handleUserList(message.data);
          break;

        case MessageType.USER_JOINED:
          this.handleUserJoined(message.data);
          break;

        case MessageType.USER_LEFT:
          this.handleUserLeft(message.data);
          break;

        case MessageType.USER_STATUS:
          this.handleUserStatus(message.data);
          break;

        case MessageType.OPERATION:
          this.handleOperation(message.data);
          break;

        case MessageType.OPERATION_HISTORY:
          this.handleOperationHistory(message.data);
          break;

        case MessageType.ERROR:
          this.handleErrorMessage(message.data);
          break;

        default:
          console.warn('收到未知类型的消息:', message);
      }

      this.emit('message', message);
    } catch (error) {
      console.error('处理消息时出错:', error);
    }
  }

  /**
   * 处理错误事件
   * @param event 错误事件
   */
  private handleError(event: any): void {
    console.error('WebSocket错误:', event);
    this.emit('error', event);
  }

  /**
   * 处理用户列表消息
   * @param data 用户列表数据
   */
  private handleUserList(data: { users: CollaborationUser[] }): void {
    this.users.clear();

    for (const user of data.users) {
      this.users.set(user.id, user);

      // 设置用户角色到权限服务
      permissionService.setUserRole(user.id, user.role);
    }

    store.dispatch(setActiveUsers(data.users));
    this.emit('userList', data.users);

    // 初始化权限服务
    permissionService.initialize();
  }

  /**
   * 处理用户加入消息
   * @param user 用户数据
   */
  private handleUserJoined(user: CollaborationUser): void {
    this.users.set(user.id, user);
    store.dispatch(addUser(user));

    // 设置用户角色到权限服务
    permissionService.setUserRole(user.id, user.role);

    this.emit('userJoined', user);

    if (user.id !== this.userId) {
      message.info(`${user.name} 已加入协作`);
    }
  }

  /**
   * 处理用户离开消息
   * @param data 用户离开数据
   */
  private handleUserLeft(data: { userId: string, userName: string }): void {
    const user = this.users.get(data.userId);

    if (user) {
      this.users.delete(data.userId);
      store.dispatch(removeUser(data.userId));
      this.emit('userLeft', user);

      if (data.userId !== this.userId) {
        message.info(`${data.userName} 已离开协作`);
      }
    }
  }

  /**
   * 处理用户状态消息
   * @param data 用户状态数据
   */
  private handleUserStatus(data: { userId: string } & Partial<CollaborationUser>): void {
    const user = this.users.get(data.userId);

    if (user) {
      // 更新用户状态
      Object.assign(user, data);
      store.dispatch(updateUserStatus({ userId: data.userId, updates: data }));

      // 如果角色发生变化，更新权限服务
      if (data.role && data.role !== user.role) {
        permissionService.setUserRole(data.userId, data.role);
      }

      this.emit('userStatus', user);
    }
  }

  /**
   * 处理操作消息
   * @param operation 操作数据
   */
  private handleOperation(operation: Operation): void {
    // 忽略自己发送的操作
    if (operation.userId === this.userId) {
      return;
    }

    // 添加到操作历史
    this.operationHistory.push(operation);
    store.dispatch(addOperation(operation));

    this.emit('operation', operation);

    // 根据操作类型执行不同的处理
    switch (operation.type) {
      case OperationType.CURSOR_MOVE:
        this.handleCursorMove(operation);
        break;

      case OperationType.SELECTION_CHANGE:
        this.handleSelectionChange(operation);
        break;

      case OperationType.EDITING_ZONE_UPDATE:
        this.handleEditingZoneUpdate(operation);
        break;

      case OperationType.EDITING_ZONE_CLEAR:
        this.handleEditingZoneClear(operation);
        break;

      // 其他操作类型的处理...
    }
  }

  /**
   * 处理操作历史消息
   * @param data 操作历史数据
   */
  private handleOperationHistory(data: { operations: Operation[] }): void {
    this.operationHistory = data.operations;
    store.dispatch(setOperations(data.operations));
    this.emit('operationHistory', data.operations);
  }

  /**
   * 处理错误消息
   * @param data 错误数据
   */
  private handleErrorMessage(data: { message: string, code: number }): void {
    console.error('服务器错误:', data);
    message.error(`协作服务器错误: ${data.message}`);
    this.emit('serverError', data);
  }

  /**
   * 处理光标移动操作
   * @param operation 操作数据
   */
  private handleCursorMove(operation: Operation): void {
    const user = this.users.get(operation.userId);

    if (user) {
      user.cursor = operation.data;
      user.lastActivity = Date.now();
      user.isActive = true;

      store.dispatch(updateUserStatus({
        userId: operation.userId,
        updates: {
          cursor: operation.data,
          lastActivity: Date.now(),
          isActive: true
        }
      }));
    }
  }

  /**
   * 处理选择变更操作
   * @param operation 操作数据
   */
  private handleSelectionChange(operation: Operation): void {
    // 实现选择变更处理逻辑
  }

  /**
   * 处理编辑区域更新操作
   * @param operation 操作数据
   */
  private handleEditingZoneUpdate(operation: Operation): void {
    // 发出编辑区域更新事件，让冲突预防服务处理
    this.emit('editingZoneUpdate', operation);
  }

  /**
   * 处理编辑区域清除操作
   * @param operation 操作数据
   */
  private handleEditingZoneClear(operation: Operation): void {
    // 发出编辑区域清除事件，让冲突预防服务处理
    this.emit('editingZoneClear', operation);
  }

  /**
   * 发送消息到服务器
   * @param type 消息类型
   * @param data 消息数据
   */
  private sendMessage(type: MessageType, data: any): void {
    if (!this.connectionManager) {
      return;
    }

    this.connectionManager.send(type, data);
  }

  /**
   * 处理操作队列
   */
  private processOperationQueue(): void {
    if (this.status !== CollaborationStatus.CONNECTED || this.operationQueue.length === 0) {
      return;
    }

    console.log(`处理 ${this.operationQueue.length} 个排队的操作`);

    for (const operation of this.operationQueue) {
      this.sendMessage(MessageType.OPERATION, operation);
      this.operationHistory.push(operation);
      store.dispatch(addOperation(operation));
    }

    this.operationQueue = [];
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }
}

// 创建单例实例
export const collaborationService = new CollaborationService();
